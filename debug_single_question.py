#!/usr/bin/env python3
"""
调试单个问题的脚本
"""

import json
import requests
import re

class OllamaClient:
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "qwen2.5:7b"):
        self.base_url = base_url.rstrip('/')
        self.model = model
    
    def generate(self, prompt: str, temperature: float = 0.1) -> str:
        try:
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "top_p": 0.9,
                    "top_k": 40
                }
            }
            
            response = requests.post(f"{self.base_url}/api/generate", json=payload, timeout=60)
            response.raise_for_status()
            
            result = response.json()
            return result.get("response", "").strip()
            
        except Exception as e:
            print(f"生成回答失败: {e}")
            return ""

def extract_answer(response: str) -> str:
    """从模型回答中提取答案标签"""
    if not response:
        return ""
    
    response = response.strip()
    print(f"🔍 模型原始回答: {response}")
    
    response_upper = response.upper()
    
    # 方法1: 查找单独的选项字母
    patterns = [
        r'答案[是为]([ABCD])',
        r'选择([ABCD])',
        r'([ABCD])选项',
        r'([ABCD])[。，,\.]',
        r'^([ABCD])$',  # 只有一个字母
        r'[^A-Z]([ABCD])[^A-Z]',  # 被非字母包围的选项
    ]
    
    for pattern in patterns:
        match = re.search(pattern, response_upper)
        if match:
            answer = match.group(1)
            print(f"✅ 提取到答案: {answer}")
            return answer
    
    # 方法2: 查找最后出现的A、B、C、D
    for option in ["D", "C", "B", "A"]:  # 倒序查找，优先最后出现的
        if option in response_upper:
            print(f"✅ 提取到答案: {option}")
            return option
    
    print("❌ 未能提取到有效答案")
    return ""

def test_single_question():
    """测试单个问题"""
    
    # 加载第二个问题（第一个问题是上下文相关的）
    with open("data/MedQA/test-2zh.jsonl", 'r', encoding='utf-8') as f:
        f.readline()  # 跳过第一行
        line = f.readline()  # 读取第二行
        item = json.loads(line.strip())
    
    question = item["question"]
    correct_answer = item["answer_idx"]
    options = item["options"]
    
    print("=" * 60)
    print("🔍 调试单个问题")
    print("=" * 60)
    print(f"问题: {question}")
    print(f"正确答案: {correct_answer}")
    print("选项:")
    for key in ["A", "B", "C", "D"]:
        if key in options:
            print(f"  {key}: {options[key]}")
    
    # 构建选项列表
    option_list = []
    for key in ["A", "B", "C", "D"]:
        if key in options:
            option_list.append(options[key])
    
    # 测试不同的提示词
    prompts = [
        # 提示词1: 原始版本
        f"""以下是一些医学资料：无

给定问题：{question}，以下哪个答案是正确的：{", ".join([f"{chr(65+i)}: {opt}" for i, opt in enumerate(option_list)])}。

您只能用精确的词语输出预测标签。请只回答A、B、C、D中的一个字母，不要添加任何其他内容。

答案：""",
        
        # 提示词2: 更简洁版本
        f"""问题：{question}

选项：
{chr(10).join([f"{chr(65+i)}: {opt}" for i, opt in enumerate(option_list)])}

请选择正确答案，只回答A、B、C或D：""",
        
        # 提示词3: 中文版本
        f"""这是一道医学选择题：

{question}

A. {option_list[0]}
B. {option_list[1]}
C. {option_list[2]}
D. {option_list[3]}

请选择正确答案（只回答字母）："""
    ]
    
    client = OllamaClient()
    
    for i, prompt in enumerate(prompts, 1):
        print(f"\n--- 测试提示词 {i} ---")
        print(f"提示词:\n{prompt}")
        print("\n" + "-" * 40)
        
        response = client.generate(prompt)
        predicted = extract_answer(response)
        
        status = "✅ 正确" if predicted == correct_answer else "❌ 错误"
        print(f"结果: {status} (预测={predicted}, 正确={correct_answer})")

if __name__ == "__main__":
    test_single_question()
