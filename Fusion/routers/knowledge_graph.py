"""
知识图谱管理API路由
"""

from fastapi import APIRouter, HTTPException, Path
from typing import List, Dict, Any

from Fusion.models.schemas import ConfigResponse, CreateKnowledgeGraphRequest, CreateKnowledgeGraphResponse
from Fusion.services.knowledge_graph_service import knowledge_graph_service

# 创建路由
router = APIRouter(prefix="/api/knowledge-graph", tags=["Knowledge Graph"])

@router.get("/configs", response_model=List[Dict])
async def list_configs():
    """
    获取所有知识图谱配置文件列表
    
    Returns:
        List[Dict]: 配置文件列表
    """
    try:
        configs = knowledge_graph_service.get_config_list()
        return configs
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取配置列表失败: {str(e)}"
        )

@router.get("/configs/{config_id}", response_model=Dict)
async def get_config(config_id: str = Path(..., description="配置ID")):
    """
    获取指定配置的详细信息
    
    Args:
        config_id: 配置ID
        
    Returns:
        Dict: 配置详细信息
    """
    try:
        config = knowledge_graph_service.get_config_by_id(config_id)
        
        if not config:
            raise HTTPException(
                status_code=404,
                detail=f"未找到配置ID: {config_id}"
            )
        
        return config
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取配置详情失败: {str(e)}"
        )

@router.post("/create", response_model=CreateKnowledgeGraphResponse)
async def create_graph(request: CreateKnowledgeGraphRequest):
    """
    根据选定的配置创建知识图谱
    
    Args:
        request: 创建知识图谱请求
        
    Returns:
        CreateKnowledgeGraphResponse: 创建结果
    """
    try:
        # 检查配置是否存在
        config = knowledge_graph_service.get_config_by_id(request.config_id)
        
        if not config:
            raise HTTPException(
                status_code=404,
                detail=f"未找到配置ID: {request.config_id}"
            )
        
        # 获取文件路径
        filename = config.get("filename")
        if not filename:
            raise HTTPException(
                status_code=400,
                detail="配置中缺少文件名信息"
            )
        
        # 构建文件路径
        import os
        from config.settings import settings
        file_path = os.path.join(settings.upload_dir, filename)
        
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=404,
                detail=f"未找到数据文件: {filename}"
            )
        
        # 创建知识图谱
        result = knowledge_graph_service._create_knowledge_graph_direct(
            config_id=request.config_id,
            entity_config=request.entity_config,
            relationship_config=request.relationship_config,
            file_path=file_path
        )
        
        return CreateKnowledgeGraphResponse(
            success=result.get("success", False),
            message=result.get("message", ""),
            task_id=None  # 简化版本不使用任务ID
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"创建知识图谱失败: {str(e)}"
        )

@router.delete("/configs/{config_id}")
async def delete_config(config_id: str = Path(..., description="配置ID")):
    """
    删除指定的配置文件
    
    Args:
        config_id: 配置ID
        
    Returns:
        Dict: 删除结果
    """
    try:
        import os
        from config.settings import settings
        
        config_path = os.path.join(settings.config_dir, f"{config_id}.json")
        
        if not os.path.exists(config_path):
            raise HTTPException(
                status_code=404,
                detail=f"未找到配置ID: {config_id}"
            )
        
        os.remove(config_path)
        
        return {
            "success": True,
            "message": f"配置 {config_id} 已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"删除配置失败: {str(e)}"
        )

@router.get("/test")
async def test_knowledge_graph():
    """
    测试知识图谱服务
    """
    return {
        "message": "知识图谱服务正常运行",
        "available_endpoints": [
            "GET /api/knowledge-graph/configs - 获取配置列表",
            "GET /api/knowledge-graph/configs/{config_id} - 获取配置详情",
            "POST /api/knowledge-graph/create - 创建知识图谱",
            "DELETE /api/knowledge-graph/configs/{config_id} - 删除配置"
        ]
    }
