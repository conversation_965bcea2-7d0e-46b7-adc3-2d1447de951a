"""
融合查询服务
"""

import time
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# 导入KG和RAG模块
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from KG.KG_query.main import query_knowledge_graph, initialize_kg_query
from RAG.main import rag_query, initialize_rag_system

from Fusion.models.schemas import (
    FusionQueryRequest, FusionQueryResponse, FusionResult,
    KGQueryResult, RAGQueryResult, QueryWeights
)
from Fusion.services.llm_service import llm_service

class FusionService:
    """融合查询服务"""
    
    def __init__(self):
        """初始化融合服务"""
        self.kg_initialized = False
        self.rag_initialized = False
        self._initialize_modules()
    
    def _initialize_modules(self):
        """初始化KG和RAG模块"""
        # 初始化KG模块
        if initialize_kg_query():
            self.kg_initialized = True
        else:
            self.kg_initialized = False

        # 初始化RAG模块
        if initialize_rag_system():
            self.rag_initialized = True
        else:
            self.rag_initialized = False
    
    async def fusion_query(self, request: FusionQueryRequest) -> FusionQueryResponse:
        """
        执行融合查询
        
        Args:
            request: 融合查询请求
            
        Returns:
            FusionQueryResponse: 融合查询响应
        """
        start_time = time.time()

        # 并行执行KG和RAG查询
        kg_result, rag_result = await self._parallel_query(request)
        print(kg_result)
        print("*"*50)
        print(rag_result)
        # 融合结果
        fusion_results = self._fuse_results(kg_result, rag_result, request.weights)

        # 准备LLM输入
        llm_kg_data = kg_result if kg_result and kg_result.get("success") else None
        llm_rag_data = rag_result.get("results", []) if rag_result and rag_result.get("success") else []

        # 构建融合上下文
        fusion_context = {
            "total_sources": len(fusion_results),
            "confidence_score": self._calculate_confidence_score(fusion_results),
            "weights_used": request.weights.dict()
        }

        # 调用LLM生成最终答案
        llm_response = llm_service.generate_fusion_answer(
            user_query=request.query,
            kg_results=llm_kg_data,
            rag_results=llm_rag_data,
            fusion_context=fusion_context
        )

        processing_time = time.time() - start_time

        return FusionQueryResponse(
            success=True,
            query=request.query,
            final_answer=llm_response.get("final_answer", ""),
            fusion_results=fusion_results,
            llm_processing_info=llm_response.get("processing_info", {}),
            total_results=len(fusion_results),
            processing_time=processing_time
        )
    
    async def _parallel_query(self, request: FusionQueryRequest) -> Tuple[Optional[Dict], Optional[Dict]]:
        """并行执行KG和RAG查询"""
        
        def kg_query_task():
            """KG查询任务"""
            if not self.kg_initialized:
                return None
            
            if not (request.enable_kg_base or request.enable_kg_incremental):
                return None
            
            return query_knowledge_graph(request.query)
        
        def rag_query_task():
            """RAG查询任务"""
            if not self.rag_initialized:
                return None
            
            if not (request.enable_rag_base or request.enable_rag_incremental):
                return None
            
            # 构建要搜索的集合列表
            collections = []
            if request.enable_rag_base:
                collections.append("base")
            if request.enable_rag_incremental:
                collections.append("incremental")

            return rag_query(
                query=request.query,
                collections=collections,
                top_k=request.top_k
            )
        
        # 使用线程池并行执行
        with ThreadPoolExecutor(max_workers=2) as executor:
            kg_future = executor.submit(kg_query_task)
            rag_future = executor.submit(rag_query_task)
            
            kg_result = kg_future.result()
            rag_result = rag_future.result()
        
        return kg_result, rag_result
    
    def _fuse_results(
        self, 
        kg_result: Optional[Dict], 
        rag_result: Optional[Dict], 
        weights: QueryWeights
    ) -> List[FusionResult]:
        """融合KG和RAG结果"""
        
        fusion_results = []
        
        # 处理KG结果
        if kg_result and kg_result.get("success"):
            kg_query_result = KGQueryResult(**kg_result)
            
            # KG基础数据结果
            fusion_results.append(FusionResult(
                kg_results=kg_query_result,
                rag_results=None,
                weighted_score=weights.kg_base_weight,
                source_type="kg_base"
            ))
            
            # 如果有增量数据权重，也添加增量结果（目前KG增量数据与基础数据相同）
            if weights.kg_incremental_weight > 0:
                fusion_results.append(FusionResult(
                    kg_results=kg_query_result,
                    rag_results=None,
                    weighted_score=weights.kg_incremental_weight,
                    source_type="kg_incremental"
                ))
        
        # 处理RAG结果
        if rag_result and rag_result.get("success"):
            rag_query_result = RAGQueryResult(**rag_result)
            rag_results = rag_result.get("results", [])
            
            # 分离基础数据和增量数据结果
            base_results = [r for r in rag_results if r.get("collection_type") == "base"]
            incremental_results = [r for r in rag_results if r.get("collection_type") == "incremental"]
            
            # RAG基础数据结果
            if base_results and weights.rag_base_weight > 0:
                for result in base_results:
                    fusion_results.append(FusionResult(
                        kg_results=None,
                        rag_results=rag_query_result,
                        weighted_score=result.get("weighted_score", 0) * weights.rag_base_weight,
                        source_type="rag_base"
                    ))
            
            # RAG增量数据结果
            if incremental_results and weights.rag_incremental_weight > 0:
                for result in incremental_results:
                    fusion_results.append(FusionResult(
                        kg_results=None,
                        rag_results=rag_query_result,
                        weighted_score=result.get("weighted_score", 0) * weights.rag_incremental_weight,
                        source_type="rag_incremental"
                    ))
        
        # 按权重分数排序
        fusion_results.sort(key=lambda x: x.weighted_score, reverse=True)
        
        return fusion_results
    
    def _calculate_confidence_score(self, fusion_results: List[FusionResult]) -> float:
        """计算融合结果的置信度分数"""
        if not fusion_results:
            return 0.0
        
        # 基于结果数量和权重分数计算置信度
        total_score = sum(result.weighted_score for result in fusion_results)
        result_count = len(fusion_results)
        
        # 归一化置信度分数 (0-1)
        confidence = min(total_score / max(result_count, 1), 1.0)
        return confidence

# 全局融合服务实例（懒加载）
_fusion_service_instance = None

def get_fusion_service() -> FusionService:
    """获取融合服务实例（懒加载）"""
    global _fusion_service_instance
    if _fusion_service_instance is None:
        _fusion_service_instance = FusionService()
    return _fusion_service_instance

# 为了向后兼容，保留原有的访问方式
fusion_service = get_fusion_service()
