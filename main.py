#!/usr/bin/env python3
"""
多模型RAG对比实验主程序
对比每个模型在无RAG和有RAG两种情况下的性能：
- qwen2.5:7b (无RAG vs 有RAG)
- llama2:7b (无RAG vs 有RAG)
- vicuna:7b (无RAG vs 有RAG)

评测指标：准确率、F1分数、精确率、召回率
数据集：MedQA中文数据集
"""

import json
import time
import requests
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from typing import Dict, List, Any, Tuple
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入RAG模块
from RAG.main import rag_query, initialize_rag_system

class OllamaClient:
    """Ollama客户端，支持多个模型"""

    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url.rstrip('/')
        self.available_models = []
        self._test_connection()

    def _test_connection(self):
        """测试ollama连接并获取可用模型"""
        try:
            response = requests.get(f"{self.base_url}/api/tags")
            response.raise_for_status()
            models = response.json().get("models", [])
            self.available_models = [m["name"] for m in models]
            print(f"✅ Ollama连接成功，可用模型: {self.available_models}")
        except Exception as e:
            print(f"❌ Ollama连接失败: {e}")
            raise

    def is_model_available(self, model: str) -> bool:
        """检查模型是否可用"""
        return model in self.available_models
    
    def generate(self, prompt: str, model: str, temperature: float = 0.1) -> str:
        """生成回答"""
        try:
            if not self.is_model_available(model):
                print(f"❌ 模型 {model} 不可用")
                return ""

            payload = {
                "model": model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "top_p": 0.9,
                    "top_k": 40
                }
            }

            response = requests.post(f"{self.base_url}/api/generate", json=payload, timeout=120)
            response.raise_for_status()

            result = response.json()
            return result.get("response", "").strip()

        except Exception as e:
            print(f"生成回答失败 (模型: {model}): {e}")
            return ""

class MedQAEvaluator:
    """MedQA评测器 - 多模型无RAG vs有RAG对比"""

    def __init__(self):
        self.ollama_client = OllamaClient()
        self.rag_initialized = False

        # 支持的模型列表
        self.models = ["qwen2.5:7b", "llama3:8b", "vicuna:7b"]

        # 初始化RAG系统
        self._initialize_systems()

        # 提示词模板
        self.prompt_template = """以下是一些医学资料：[f]

给定问题：[q]，以下哪个答案是正确的：[a1, a2, a3, a4]。

您只能用精确的词语输出预测标签。请只回答A、B、C、D中的一个字母，不要添加任何其他内容。

答案："""
    
    def _initialize_systems(self):
        """初始化RAG系统"""
        print("🔧 初始化系统...")

        # 初始化RAG系统
        try:
            self.rag_initialized = initialize_rag_system()
            if self.rag_initialized:
                print("✅ RAG系统初始化成功")
            else:
                print("❌ RAG系统初始化失败")
        except Exception as e:
            print(f"❌ RAG系统初始化异常: {e}")
            self.rag_initialized = False

        # 检查模型可用性
        print("🔍 检查模型可用性...")
        available_models = []
        for model in self.models:
            if self.ollama_client.is_model_available(model):
                available_models.append(model)
                print(f"✅ 模型 {model} 可用")
            else:
                print(f"❌ 模型 {model} 不可用")

        self.models = available_models
        if not self.models:
            print("❌ 没有可用的模型")
        else:
            print(f"📋 将使用模型: {self.models}")
    
    def load_medqa_data(self, file_path: str, limit: int = None) -> List[Dict]:
        """加载MedQA数据"""
        print(f"📂 加载数据: {file_path}")

        data = []
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if limit and len(data) >= limit:
                    break
                try:
                    item = json.loads(line.strip())
                    # 跳过上下文相关的问题（包含"承上题"的问题）
                    if "承上题" in item["question"]:
                        continue
                    data.append(item)
                except json.JSONDecodeError as e:
                    print(f"第{line_num}行JSON解析错误: {e}")
                    continue

        print(f"✅ 成功加载 {len(data)} 条数据")
        return data
    
    def format_question(self, item: Dict) -> Tuple[str, str, List[str]]:
        """格式化问题"""
        question = item["question"]
        correct_answer = item["answer_idx"]  # 使用answer_idx而不是answer
        options = item["options"]

        # 构建选项列表
        option_list = []
        for key in ["A", "B", "C", "D"]:
            if key in options:
                option_list.append(options[key])

        return question, correct_answer, option_list
    
    def method_no_rag(self, question: str, options: List[str], model: str) -> str:
        """无RAG增强方法 - 直接使用LLM"""
        prompt = self.prompt_template.replace("[f]", "无")
        prompt = prompt.replace("[q]", question)
        prompt = prompt.replace("[a1, a2, a3, a4]", ", ".join([f"{chr(65+i)}: {opt}" for i, opt in enumerate(options)]))

        response = self.ollama_client.generate(prompt, model)
        return self._extract_answer(response)

    def method_with_rag(self, question: str, options: List[str], model: str) -> str:
        """RAG增强方法"""
        if not self.rag_initialized:
            # 如果RAG未初始化，回退到无RAG方法
            return self.method_no_rag(question, options, model)

        # 查询RAG获取相关文档
        rag_result = rag_query(question, collections=["incremental"], top_k=3)
        rag_knowledge = self._extract_rag_knowledge(rag_result)

        prompt = self.prompt_template.replace("[f]", rag_knowledge)
        prompt = prompt.replace("[q]", question)
        prompt = prompt.replace("[a1, a2, a3, a4]", ", ".join([f"{chr(65+i)}: {opt}" for i, opt in enumerate(options)]))

        response = self.ollama_client.generate(prompt, model)
        return self._extract_answer(response)
    
    def _extract_rag_knowledge(self, rag_result: Dict) -> str:
        """从RAG结果中提取知识"""
        if not rag_result or not rag_result.get("success"):
            return "无相关文档信息"
        
        results = rag_result.get("results", [])
        if not results:
            return "无相关文档信息"
        
        knowledge_texts = []
        for result in results[:3]:  # 只取前3个结果
            content = result.get("content", "").strip()
            if content:
                # 截取前200字符避免prompt过长
                knowledge_texts.append(content[:200])
        
        return "; ".join(knowledge_texts) if knowledge_texts else "无相关文档信息"
    
    def _extract_answer(self, response: str) -> str:
        """从模型回答中提取答案标签"""
        if not response:
            return ""

        response = response.strip()
        print(f"🔍 模型原始回答: {response}")  # 调试信息

        response_upper = response.upper()

        # 方法1: 查找单独的选项字母
        import re

        # 查找形如 "答案是A" 或 "选择A" 或 "A选项" 的模式
        patterns = [
            r'答案[是为]([ABCD])',
            r'选择([ABCD])',
            r'([ABCD])选项',
            r'([ABCD])[。，,\.]',
            r'^([ABCD])$',  # 只有一个字母
            r'[^A-Z]([ABCD])[^A-Z]',  # 被非字母包围的选项
        ]

        for pattern in patterns:
            match = re.search(pattern, response_upper)
            if match:
                answer = match.group(1)
                print(f"✅ 提取到答案: {answer}")  # 调试信息
                return answer

        # 方法2: 查找最后出现的A、B、C、D
        for option in ["D", "C", "B", "A"]:  # 倒序查找，优先最后出现的
            if option in response_upper:
                print(f"✅ 提取到答案: {option}")  # 调试信息
                return option

        # 如果没有找到明确的选项，返回空字符串
        print("❌ 未能提取到有效答案")  # 调试信息
        return ""

    def evaluate_model_method(self, model_name: str, method_name: str, method_func, test_data: List[Dict], max_samples: int = None) -> Dict:
        """评测单个模型的单个方法"""
        print(f"\n🔍 评测: {model_name} - {method_name}")

        if max_samples:
            test_data = test_data[:max_samples]

        predictions = []
        true_labels = []
        correct_count = 0
        total_count = len(test_data)

        start_time = time.time()

        for i, item in enumerate(test_data, 1):
            try:
                question, correct_answer, options = self.format_question(item)

                # 使用指定方法获取预测
                predicted_answer = method_func(question, options, model_name)

                predictions.append(predicted_answer)
                true_labels.append(correct_answer)

                # 显示预测结果对比
                status = "✅" if predicted_answer == correct_answer else "❌"
                print(f"{status} 问题{i}: 预测={predicted_answer}, 正确={correct_answer}")

                if predicted_answer == correct_answer:
                    correct_count += 1

                # 显示进度
                if i % 10 == 0 or i == total_count:
                    accuracy = correct_count / i
                    elapsed = time.time() - start_time
                    avg_time = elapsed / i
                    eta = avg_time * (total_count - i)
                    print(f"进度: {i}/{total_count} ({i/total_count*100:.1f}%) | "
                          f"准确率: {accuracy:.3f} | "
                          f"预计剩余: {eta:.1f}s")

            except Exception as e:
                print(f"处理第{i}个样本时出错: {e}")
                predictions.append("")
                true_labels.append(correct_answer)

        # 计算评测指标
        # 将空预测视为错误预测，随机分配一个选项
        processed_predictions = []
        for pred in predictions:
            if pred in ["A", "B", "C", "D"]:
                processed_predictions.append(pred)
            else:
                processed_predictions.append("A")  # 默认选择A

        accuracy = accuracy_score(true_labels, processed_predictions)

        # 计算F1分数（多分类平均）
        f1_macro = f1_score(true_labels, processed_predictions, average='macro', zero_division=0)
        f1_weighted = f1_score(true_labels, processed_predictions, average='weighted', zero_division=0)

        precision_macro = precision_score(true_labels, processed_predictions, average='macro', zero_division=0)
        recall_macro = recall_score(true_labels, processed_predictions, average='macro', zero_division=0)

        elapsed_time = time.time() - start_time

        results = {
            "model_name": model_name,
            "method_name": method_name,
            "combination_name": f"{model_name} - {method_name}",
            "accuracy": accuracy,
            "f1_macro": f1_macro,
            "f1_weighted": f1_weighted,
            "precision_macro": precision_macro,
            "recall_macro": recall_macro,
            "total_samples": total_count,
            "correct_predictions": correct_count,
            "elapsed_time": elapsed_time,
            "avg_time_per_sample": elapsed_time / total_count,
            "predictions": processed_predictions,
            "true_labels": true_labels
        }

        print(f"✅ {model_name} - {method_name} 评测完成:")
        print(f"   准确率: {accuracy:.3f}")
        print(f"   F1分数(macro): {f1_macro:.3f}")
        print(f"   F1分数(weighted): {f1_weighted:.3f}")
        print(f"   总耗时: {elapsed_time:.1f}s")
        print(f"   平均耗时: {elapsed_time/total_count:.2f}s/样本")

        return results

    def run_comparison_experiment(self, test_file: str, max_samples: int = 100, output_dir: str = "evaluation_results"):
        """运行多模型无RAG vs 有RAG对比实验"""
        print("🚀 开始多模型无RAG vs 有RAG对比实验")
        print("=" * 60)

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 加载测试数据
        test_data = self.load_medqa_data(test_file, limit=max_samples)

        if not test_data:
            print("❌ 无法加载测试数据")
            return

        if not self.models:
            print("❌ 没有可用的模型")
            return

        # 定义评测方法
        methods = [
            ("无RAG", self.method_no_rag),
            ("有RAG", self.method_with_rag)
        ]

        # 运行评测 - 每个模型的每种方法
        all_results = []
        for model in self.models:
            for method_name, method_func in methods:
                try:
                    result = self.evaluate_model_method(model, method_name, method_func, test_data, max_samples)
                    all_results.append(result)
                except Exception as e:
                    print(f"❌ {model} - {method_name} 评测失败: {e}")
                    continue

        if not all_results:
            print("❌ 所有评测都失败了")
            return

        # 保存详细结果
        self._save_detailed_results(all_results, output_dir)

        # 生成对比图表
        self._generate_comparison_plots(all_results, output_dir)

        # 生成对比表格
        self._generate_comparison_table(all_results, output_dir)

        # 打印总结
        self._print_summary(all_results)

        print(f"\n✅ 实验完成！结果保存在 {output_dir} 目录中")

    def _save_detailed_results(self, results: List[Dict], output_dir: str):
        """保存详细结果到JSON文件"""
        # 准备保存的数据（移除不能序列化的部分）
        save_data = []
        for result in results:
            save_result = result.copy()
            # 移除预测和真实标签（太长了）
            save_result.pop("predictions", None)
            save_result.pop("true_labels", None)
            save_data.append(save_result)

        # 保存到JSON文件
        results_file = os.path.join(output_dir, "evaluation_results.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

        print(f"📊 详细结果已保存到: {results_file}")

    def _generate_comparison_plots(self, results: List[Dict], output_dir: str):
        """生成对比图表 - 每个指标单独一个图"""
        if not results:
            return

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 提取数据 - 使用组合名称
        combination_names = [r["combination_name"] for r in results]
        accuracies = [r["accuracy"] for r in results]
        f1_scores = [r["f1_macro"] for r in results]
        precisions = [r["precision_macro"] for r in results]
        recalls = [r["recall_macro"] for r in results]

        # 定义颜色 - 为每个模型-方法组合分配颜色
        colors = []
        for i, result in enumerate(results):
            if "无RAG" in result["method_name"]:
                colors.append('#FF6B6B')  # 红色系 - 无RAG
            else:
                colors.append('#4ECDC4')  # 绿色系 - 有RAG

        # 1. 准确率对比图
        self._create_single_metric_plot(
            combination_names, accuracies, "Accuracy", "准确率",
            colors, output_dir, "accuracy_comparison.png"
        )

        # 2. F1分数对比图
        self._create_single_metric_plot(
            combination_names, f1_scores, "F1 Score (Macro)", "F1分数",
            colors, output_dir, "f1_comparison.png"
        )

        # 3. 精确率对比图
        self._create_single_metric_plot(
            combination_names, precisions, "Precision (Macro)", "精确率",
            colors, output_dir, "precision_comparison.png"
        )

        # 4. 召回率对比图
        self._create_single_metric_plot(
            combination_names, recalls, "Recall (Macro)", "召回率",
            colors, output_dir, "recall_comparison.png"
        )

        print(f"📈 所有对比图表已保存到: {output_dir}")

    def _create_single_metric_plot(self, combination_names: List[str], values: List[float],
                                 title: str, ylabel: str, colors: List[str],
                                 output_dir: str, filename: str):
        """创建单个指标的对比图"""
        fig, ax = plt.subplots(1, 1, figsize=(14, 8))

        bars = ax.bar(combination_names, values, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
        ax.set_title(f'{title} Comparison - 无RAG vs 有RAG', fontsize=16, fontweight='bold', pad=20)
        ax.set_ylabel(ylabel, fontsize=14)
        ax.set_xlabel('模型-方法组合', fontsize=14)
        ax.set_ylim(0, 1)
        ax.grid(True, alpha=0.3, axis='y')

        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{value:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=10)

        # 美化图表
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.tick_params(axis='x', labelsize=10, rotation=45)
        ax.tick_params(axis='y', labelsize=12)

        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='#FF6B6B', label='无RAG'),
            Patch(facecolor='#4ECDC4', label='有RAG')
        ]
        ax.legend(handles=legend_elements, loc='upper right')

        plt.tight_layout()

        # 保存图表
        plot_file = os.path.join(output_dir, filename)
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"📊 {title}图表已保存到: {plot_file}")

    def _generate_comparison_table(self, results: List[Dict], output_dir: str):
        """生成对比表格"""
        if not results:
            return

        # 创建DataFrame
        table_data = []
        for result in results:
            table_data.append({
                'Model': result['model_name'],
                'Method': result['method_name'],
                'Combination': result['combination_name'],
                'Accuracy': f"{result['accuracy']:.3f}",
                'F1 Score': f"{result['f1_macro']:.3f}",
                'Precision': f"{result['precision_macro']:.3f}",
                'Recall': f"{result['recall_macro']:.3f}",
                'Samples': result['total_samples'],
                'Correct': result['correct_predictions'],
                'Time (s)': f"{result['elapsed_time']:.1f}",
                'Time/Sample (s)': f"{result['avg_time_per_sample']:.2f}"
            })

        df = pd.DataFrame(table_data)

        # 保存为CSV
        csv_file = os.path.join(output_dir, "comparison_table.csv")
        df.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"📋 对比表格(CSV)已保存到: {csv_file}")

        # 生成格式化的文本表格
        txt_file = os.path.join(output_dir, "comparison_table.txt")
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write("多模型无RAG vs 有RAG性能对比表\n")
            f.write("=" * 100 + "\n\n")

            # 写入表格头
            f.write(f"{'Model':<15} {'Method':<8} {'Accuracy':<10} {'F1 Score':<10} {'Precision':<10} {'Recall':<10} {'Samples':<8} {'Correct':<8}\n")
            f.write("-" * 100 + "\n")

            # 写入数据行
            for result in results:
                f.write(f"{result['model_name']:<15} "
                       f"{result['method_name']:<8} "
                       f"{result['accuracy']:<10.3f} "
                       f"{result['f1_macro']:<10.3f} "
                       f"{result['precision_macro']:<10.3f} "
                       f"{result['recall_macro']:<10.3f} "
                       f"{result['total_samples']:<8} "
                       f"{result['correct_predictions']:<8}\n")

            f.write("\n" + "=" * 100 + "\n")
            f.write("注：对比每个模型在无RAG和有RAG两种情况下的性能\n")
            f.write(f"测试样本数：{results[0]['total_samples']}\n")
            f.write(f"评测时间：{time.strftime('%Y-%m-%d %H:%M:%S')}\n")

        print(f"📋 对比表格(TXT)已保存到: {txt_file}")

        # 生成LaTeX格式表格
        self._generate_latex_table(results, output_dir)

        # 生成论文风格的对比表格
        self._generate_paper_style_table(results, output_dir)

    def _generate_latex_table(self, results: List[Dict], output_dir: str):
        """生成LaTeX格式的表格"""
        latex_file = os.path.join(output_dir, "comparison_table.tex")

        with open(latex_file, 'w', encoding='utf-8') as f:
            f.write("\\begin{table}[htbp]\n")
            f.write("\\centering\n")
            f.write("\\caption{多模型无RAG vs 有RAG性能对比}\n")
            f.write("\\label{tab:model_rag_comparison}\n")
            f.write("\\begin{tabular}{|l|l|c|c|c|c|}\n")
            f.write("\\hline\n")
            f.write("Model & Method & Accuracy & F1 Score & Precision & Recall \\\\\n")
            f.write("\\hline\n")

            for result in results:
                f.write(f"{result['model_name']} & "
                       f"{result['method_name']} & "
                       f"{result['accuracy']:.3f} & "
                       f"{result['f1_macro']:.3f} & "
                       f"{result['precision_macro']:.3f} & "
                       f"{result['recall_macro']:.3f} \\\\\n")

            f.write("\\hline\n")
            f.write("\\end{tabular}\n")
            f.write("\\end{table}\n")

        print(f"📋 LaTeX表格已保存到: {latex_file}")

    def _generate_paper_style_table(self, results: List[Dict], output_dir: str):
        """生成论文风格的对比表格"""
        paper_table_file = os.path.join(output_dir, "paper_style_table.txt")

        # 按模型分组数据
        models_data = {}
        for result in results:
            model = result['model_name']
            if model not in models_data:
                models_data[model] = {}
            models_data[model][result['method_name']] = result

        with open(paper_table_file, 'w', encoding='utf-8') as f:
            f.write("论文风格对比表格\n")
            f.write("=" * 80 + "\n\n")

            # 表格头
            f.write(f"{'Model':<15} {'Accuracy':<20} {'F1 Score':<20} {'Precision':<20} {'Recall':<20}\n")
            f.write(f"{'':15} {'无RAG':<10} {'有RAG':<10} {'无RAG':<10} {'有RAG':<10} {'无RAG':<10} {'有RAG':<10} {'无RAG':<10} {'有RAG':<10}\n")
            f.write("-" * 120 + "\n")

            # 数据行
            for model, methods in models_data.items():
                no_rag = methods.get('无RAG', {})
                with_rag = methods.get('有RAG', {})

                f.write(f"{model:<15} "
                       f"{no_rag.get('accuracy', 0):<10.3f} "
                       f"{with_rag.get('accuracy', 0):<10.3f} "
                       f"{no_rag.get('f1_macro', 0):<10.3f} "
                       f"{with_rag.get('f1_macro', 0):<10.3f} "
                       f"{no_rag.get('precision_macro', 0):<10.3f} "
                       f"{with_rag.get('precision_macro', 0):<10.3f} "
                       f"{no_rag.get('recall_macro', 0):<10.3f} "
                       f"{with_rag.get('recall_macro', 0):<10.3f}\n")

            f.write("\n" + "=" * 120 + "\n")
            f.write("注：对比每个模型在无RAG和有RAG两种情况下的性能差异\n")

        print(f"📋 论文风格表格已保存到: {paper_table_file}")

    def _print_summary(self, results: List[Dict]):
        """打印实验总结"""
        print("\n" + "=" * 100)
        print("🎯 多模型无RAG vs 有RAG实验总结")
        print("=" * 100)

        # 按准确率排序
        sorted_results = sorted(results, key=lambda x: x["accuracy"], reverse=True)

        print(f"{'排名':<4} {'模型-方法组合':<30} {'准确率':<10} {'F1分数':<10} {'精确率':<10} {'召回率':<10}")
        print("-" * 100)

        for i, result in enumerate(sorted_results, 1):
            print(f"{i:<4} {result['combination_name']:<30} "
                  f"{result['accuracy']:<10.3f} {result['f1_macro']:<10.3f} "
                  f"{result['precision_macro']:<10.3f} {result['recall_macro']:<10.3f}")

        # 找出最佳组合
        best_combination = sorted_results[0]
        print(f"\n🏆 最佳组合: {best_combination['combination_name']}")
        print(f"   准确率: {best_combination['accuracy']:.3f}")
        print(f"   F1分数: {best_combination['f1_macro']:.3f}")
        print(f"   精确率: {best_combination['precision_macro']:.3f}")
        print(f"   召回率: {best_combination['recall_macro']:.3f}")

        # 分析RAG的效果
        print(f"\n📊 RAG效果分析:")
        models_data = {}
        for result in results:
            model = result['model_name']
            if model not in models_data:
                models_data[model] = {}
            models_data[model][result['method_name']] = result

        for model, methods in models_data.items():
            if '无RAG' in methods and '有RAG' in methods:
                no_rag = methods['无RAG']
                with_rag = methods['有RAG']

                acc_improvement = with_rag['accuracy'] - no_rag['accuracy']
                f1_improvement = with_rag['f1_macro'] - no_rag['f1_macro']

                print(f"   {model}:")
                print(f"     准确率提升: {acc_improvement:+.3f} ({acc_improvement/no_rag['accuracy']*100:+.1f}%)")
                print(f"     F1分数提升: {f1_improvement:+.3f} ({f1_improvement/no_rag['f1_macro']*100:+.1f}%)")

        # 计算整体平均性能
        no_rag_results = [r for r in results if r['method_name'] == '无RAG']
        with_rag_results = [r for r in results if r['method_name'] == '有RAG']

        if no_rag_results and with_rag_results:
            avg_no_rag_acc = np.mean([r['accuracy'] for r in no_rag_results])
            avg_with_rag_acc = np.mean([r['accuracy'] for r in with_rag_results])
            avg_no_rag_f1 = np.mean([r['f1_macro'] for r in no_rag_results])
            avg_with_rag_f1 = np.mean([r['f1_macro'] for r in with_rag_results])

            print(f"\n📈 整体平均性能:")
            print(f"   无RAG平均准确率: {avg_no_rag_acc:.3f}")
            print(f"   有RAG平均准确率: {avg_with_rag_acc:.3f}")
            print(f"   无RAG平均F1分数: {avg_no_rag_f1:.3f}")
            print(f"   有RAG平均F1分数: {avg_with_rag_f1:.3f}")
            print(f"   RAG整体准确率提升: {avg_with_rag_acc - avg_no_rag_acc:+.3f}")
            print(f"   RAG整体F1分数提升: {avg_with_rag_f1 - avg_no_rag_f1:+.3f}")


def main():
    """主函数"""
    print("🔬 多模型无RAG vs 有RAG对比实验系统")
    print("对比每个模型在无RAG和有RAG两种情况下的性能")
    print("支持模型: qwen2.5:7b, llama3.1:8b, vicuna:7b")
    print("=" * 60)

    # 配置参数
    test_file = "data/MedQA/test-2zh.jsonl"  # 测试数据文件
    max_samples = 100  # 最大测试样本数（减少以便快速测试）
    output_dir = "evaluation_results"  # 输出目录

    # 检查测试文件是否存在
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        print("请确保MedQA数据集已正确放置在data/MedQA/目录下")
        return

    try:
        # 创建评测器
        evaluator = MedQAEvaluator()

        # 运行对比实验
        evaluator.run_comparison_experiment(
            test_file=test_file,
            max_samples=max_samples,
            output_dir=output_dir
        )

    except KeyboardInterrupt:
        print("\n⚠️ 实验被用户中断")
    except Exception as e:
        print(f"❌ 实验运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
