# KG+RAG系统模块功能总结

## RAG模块功能总结

RAG（Retrieval-Augmented Generation）模块是一个基于向量检索的知识增强系统，主要包含以下核心功能模块：

### 1. 数据预处理模块 (data_process.py)
- **多格式文档支持**：支持txt、pdf、word、json、jsonl、csv等多种文档格式的自动识别和加载
- **智能文档分块**：使用LangChain的RecursiveCharacterTextSplitter进行文档分割，优化的分块参数（chunk_size=500, chunk_overlap=100）
- **元数据管理**：为每个文档块生成唯一ID、时间戳、来源文件等元数据信息
- **Milvus格式转换**：将处理后的文档转换为Milvus向量数据库兼容的格式

### 2. 混合搜索模块 (milvus_hybrid_search.py)
- **向量嵌入功能**：集成Ollama嵌入模型（支持mxbai-embed-large、nomic-embed-text、all-minilm等多种模型）
- **密集向量搜索**：基于语义相似度的向量检索，使用内积距离进行相似度计算
- **稀疏向量搜索**：基于BM25算法的关键词匹配搜索，支持停用词过滤和词干提取
- **混合搜索算法**：结合密集向量和稀疏向量的RRF（Reciprocal Rank Fusion）融合排序
- **集合管理**：支持创建、删除、批量插入等向量数据库操作

### 3. 查询优化模块 (main.py)
- **查询预处理**：对用户查询进行清理和标准化处理
- **相关性计算**：基于关键词匹配、词汇重叠率、关键词密度等多维度计算内容相关性
- **结果后处理**：对搜索结果进行去重、排序、过滤低质量结果
- **多集合查询**：支持同时查询基础数据集合和增量数据集合
- **统一接口**：提供rag_query、rag_insert等统一的查询和插入接口

### 4. 系统管理模块
- **初始化管理**：自动初始化向量数据库连接和嵌入模型
- **集合分类**：支持base（基础数据）和incremental（增量数据）两种集合类型
- **批量处理**：支持大规模数据的批量插入和处理
- **错误处理**：完善的异常处理和错误恢复机制

## KG模块功能总结

KG（Knowledge Graph）模块是一个完整的知识图谱构建和查询系统，包含三个核心子模块：

### 1. KG_schema_discovery（模式发现模块）
- **数据模式提取**：自动从数据源中提取表结构和字段信息
- **实体配置生成**：使用LLM分析数据模式，自动生成实体类型和属性配置
- **关系模式发现**：基于数据表之间的关联关系，自动发现和配置实体间的关系
- **元图创建**：在Neo4j中创建描述知识图谱结构的元图（metagraph）
- **LLM集成**：集成DeepSeek等大语言模型进行智能模式分析

### 2. KG_builder（知识图谱构建模块）
- **Neo4j连接管理**：管理与Neo4j图数据库的连接和会话
- **实体节点创建**：根据配置的实体模式，从数据表中批量创建实体节点
- **关系构建**：基于关系配置，在实体之间建立各种类型的关系
- **数据导入**：支持从多种数据源（CSV、JSON等）导入数据到知识图谱
- **模式加载**：从元图中加载实体和关系配置信息
- **批量处理**：支持大规模数据的高效批量导入和处理

### 3. KG_query（知识图谱查询模块）
- **意图识别**：使用LLM分析用户查询，识别查询意图和目标实体类型
- **实体识别**：从用户查询中提取和识别相关实体
- **相似度匹配**：支持基于字符串相似度的模糊实体匹配（使用APOC插件）
- **Cypher查询生成**：根据查询意图生成预定义的Cypher查询语句
- **查询执行**：在Neo4j数据库中执行Cypher查询并获取结果
- **结果处理**：对查询结果进行去重、排序、限制数量等后处理
- **多策略查询**：支持精确匹配和相似度匹配两种查询策略
- **缓存优化**：对系统提示词进行缓存，提高查询效率

### KG模块特色功能
- **无LLM答案生成**：专注于知识检索，不依赖LLM生成最终答案
- **预定义查询**：使用预设的Cypher查询模板，减少LLM依赖
- **相似度阈值控制**：可配置的相似度匹配阈值（默认0.6）
- **模块化设计**：各子模块独立运行，支持灵活组合使用
- **统一接口**：提供query_knowledge_graph等统一的查询入口

## Fusion模块功能总结

Fusion（融合查询）模块是连接RAG和KG模块的核心组件，实现两种知识检索方式的协同工作：

### 1. 并行查询执行
- **异步查询调度**：同时向RAG和KG模块发送查询请求，提高响应效率
- **独立查询处理**：RAG和KG模块独立处理查询，避免相互干扰
- **超时控制**：设置查询超时机制，确保系统稳定性
- **错误隔离**：单个模块故障不影响其他模块的正常运行

### 2. 结果融合算法
- **多源结果整合**：将RAG的向量检索结果和KG的图谱查询结果进行统一整合
- **权重分配策略**：根据查询类型和领域特点，动态调整RAG和KG结果的权重
- **去重处理**：识别和合并来自不同模块的重复或相似信息
- **排序优化**：基于相关性分数和置信度对融合结果进行重新排序

### 3. 智能路由机制
- **查询类型识别**：分析用户查询特点，决定启用哪些模块
- **动态模块选择**：根据查询复杂度和数据可用性，智能选择最优的检索策略
- **负载均衡**：在多个检索模块间分配查询负载，优化系统性能
- **降级策略**：当某个模块不可用时，自动切换到可用的检索方式

### 4. 结果质量控制
- **置信度评估**：对融合后的结果进行质量评估和置信度计算
- **一致性检查**：验证不同模块返回结果的一致性和互补性
- **结果过滤**：基于质量阈值过滤低质量或不相关的结果
- **标准化输出**：将融合结果转换为统一的标准格式

## 系统集成特点

1. **模块独立性**：RAG、KG和Fusion模块可以独立运行，也可以灵活组合使用
2. **统一数据格式**：三个模块都支持标准化的查询结果格式
3. **可扩展性**：支持新增数据源、模型和查询策略
4. **性能优化**：针对医学领域数据进行了专门的参数优化
5. **错误容错**：完善的异常处理和降级策略
6. **智能融合**：通过Fusion模块实现多模态知识检索的最优组合

该系统为医学问答等领域提供了完整的知识检索和增强解决方案，结合了向量检索的语义理解能力、知识图谱的结构化推理能力，以及智能融合的协同优势。
