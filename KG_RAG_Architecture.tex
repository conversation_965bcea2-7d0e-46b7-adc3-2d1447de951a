\documentclass[10pt,a4paper,landscape]{article}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{ctex}  % 中文支持
\usepackage{tikz}
\usepackage{xcolor}
\usepackage{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{caption}
\usepackage{subcaption}

% 设置页面边距 - 横向布局，更小边距
\geometry{margin=1cm, landscape}

% 设置中文字体
\setCJKmainfont{SimSun}  % 宋体
\setCJKsansfont{SimHei}  % 黑体

% TikZ库
\usetikzlibrary{shapes.geometric, arrows, positioning, fit, backgrounds, shadows, decorations.pathreplacing}

% 定义颜色方案
\definecolor{ragblue}{RGB}{70, 130, 180}
\definecolor{kggreen}{RGB}{60, 179, 113}
\definecolor{fusionpurple}{RGB}{147, 112, 219}
\definecolor{lightgray}{RGB}{245, 245, 245}
\definecolor{darkgray}{RGB}{105, 105, 105}
\definecolor{accentorange}{RGB}{255, 140, 0}

% 定义节点样式
\tikzset{
    % RAG模块样式
    ragmodule/.style={
        rectangle, rounded corners=3pt,
        fill=ragblue!20, draw=ragblue!80, thick,
        text width=3.2cm, align=center,
        minimum height=1.6cm,
        font=\scriptsize\sffamily,
        inner sep=3pt
    },
    % KG模块样式
    kgmodule/.style={
        rectangle, rounded corners=3pt,
        fill=kggreen!20, draw=kggreen!80, thick,
        text width=3.2cm, align=center,
        minimum height=1.6cm,
        font=\scriptsize\sffamily,
        inner sep=3pt
    },
    % 融合模块样式
    fusionmodule/.style={
        rectangle, rounded corners=5pt,
        fill=fusionpurple!20, draw=fusionpurple!80, thick,
        text width=3.2cm, align=center,
        minimum height=1.6cm,
        font=\scriptsize\sffamily,
        inner sep=3pt
    },
    % 数据源样式
    datasource/.style={
        cylinder, shape border rotate=90,
        fill=lightgray, draw=darkgray, thick,
        text width=2.5cm, align=center,
        minimum height=1.4cm,
        font=\scriptsize\sffamily,
        inner sep=2pt
    },
    % 箭头样式
    arrow/.style={
        ->, >=stealth, thick
    },
    % 数据流箭头
    dataflow/.style={
        ->, >=stealth, thick, color=accentorange
    },
    % 组框样式
    groupbox/.style={
        rectangle, rounded corners=10pt,
        draw=darkgray, thick, dashed,
        fill=lightgray!30,
        inner sep=10pt
    }
}

\begin{document}

\title{KG+RAG系统架构图}
\author{}
\date{}
\maketitle

\begin{figure}[htbp]
\centering
\begin{tikzpicture}[node distance=1.2cm and 1.8cm, scale=0.85, transform shape]

% 数据源层 - 左侧
\node[datasource] (docs) {多格式文档\\(TXT, PDF, Word, JSON)};
\node[datasource, below=1cm of docs] (tables) {结构化数据\\(CSV, 数据库)};

% RAG模块组 - 中左
\node[ragmodule, right=2.2cm of docs] (dataprocess) {数据预处理模块\\文档分块\\元数据管理};
\node[ragmodule, below=of dataprocess] (hybridsearch) {混合搜索模块\\向量嵌入\\BM25+语义搜索};
\node[ragmodule, below=of hybridsearch] (queryopt) {查询优化模块\\相关性计算\\结果后处理};
\node[ragmodule, below=of queryopt] (ragmgmt) {系统管理模块\\集合管理\\批量处理};

% KG模块组 - 中右
\node[kgmodule, right=2.2cm of dataprocess] (schemadiscovery) {模式发现模块\\数据模式提取\\实体关系发现};
\node[kgmodule, below=of schemadiscovery] (kgbuilder) {图谱构建模块\\实体节点创建\\关系构建};
\node[kgmodule, below=of kgbuilder] (kgquery) {图谱查询模块\\意图识别\\Cypher查询};

% 存储层 - 中下
\node[datasource, below=1.2cm of ragmgmt] (milvus) {Milvus\\向量数据库};
\node[datasource, below=1.2cm of kgquery] (neo4j) {Neo4j\\图数据库};

% 融合层 - 右侧，加入新的融合模块
\node[fusionmodule, right=2.2cm of kgquery, yshift=-0.5cm] (fusion) {KG+RAG融合\\查询模块\\并行检索\\结果融合};

% 输出层 - 最右
\node[fusionmodule, right=2.2cm of fusion] (output) {统一查询接口\\标准化\\结果输出};

% 组框
\node[groupbox, fit=(dataprocess)(hybridsearch)(queryopt)(ragmgmt)] (raggroup) {};
\node[groupbox, fit=(schemadiscovery)(kgbuilder)(kgquery)] (kggroup) {};

% 组标签
\node[above=0.2cm of raggroup.north, font=\small\bfseries\sffamily, color=ragblue] {RAG模块};
\node[above=0.2cm of kggroup.north, font=\small\bfseries\sffamily, color=kggreen] {KG模块};

% 数据流箭头
\draw[dataflow] (docs) -- (dataprocess);
\draw[dataflow] (tables) -- (schemadiscovery);

% RAG内部流
\draw[arrow, color=ragblue] (dataprocess) -- (hybridsearch);
\draw[arrow, color=ragblue] (hybridsearch) -- (queryopt);
\draw[arrow, color=ragblue] (queryopt) -- (ragmgmt);
\draw[arrow, color=ragblue] (ragmgmt) -- (milvus);

% KG内部流
\draw[arrow, color=kggreen] (schemadiscovery) -- (kgbuilder);
\draw[arrow, color=kggreen] (kgbuilder) -- (kgquery);
\draw[arrow, color=kggreen] (kgquery) -- (neo4j);

% 查询流 - 横向流动
\draw[arrow, color=ragblue] (milvus) -- (fusion);
\draw[arrow, color=kggreen] (neo4j) -- (fusion);
\draw[arrow, color=fusionpurple] (fusion) -- (output);

% 反馈流（虚线）
\draw[arrow, dashed, color=darkgray] (fusion) to[bend left=15] (queryopt);
\draw[arrow, dashed, color=darkgray] (fusion) to[bend right=15] (kgquery);

% 添加标注
\node[below=0.2cm of fusion, font=\tiny\sffamily, text width=2.5cm, align=center] {并行执行\\RRF融合\\结果优化};

\end{tikzpicture}
\caption{KG+RAG系统整体架构图。该系统包含RAG模块（蓝色）和KG模块（绿色），通过融合层（紫色）实现协同查询。RAG模块负责向量检索和语义搜索，KG模块负责结构化知识推理，两者结合提供全面的知识增强能力。}
\label{fig:kg_rag_architecture}
\end{figure}

\newpage

% 详细模块功能图 - 横向布局
\begin{figure}[htbp]
\centering
\begin{tikzpicture}[node distance=1cm and 1.4cm, scale=0.75, transform shape]

% RAG详细模块 - 上半部分，横向排列
\node[ragmodule, text width=2.5cm, minimum height=1.6cm] (dp1) {多格式支持\\TXT/PDF/Word};
\node[ragmodule, right=of dp1, text width=2.5cm, minimum height=1.6cm] (dp2) {智能分块\\LangChain};
\node[ragmodule, right=of dp2, text width=2.5cm, minimum height=1.6cm] (dp3) {元数据管理\\ID生成};

\node[ragmodule, right=1.6cm of dp3, text width=2.5cm, minimum height=1.6cm] (hs1) {向量嵌入\\Ollama模型};
\node[ragmodule, right=of hs1, text width=2.5cm, minimum height=1.6cm] (hs2) {密集向量\\语义搜索};
\node[ragmodule, right=of hs2, text width=2.5cm, minimum height=1.6cm] (hs3) {稀疏向量\\BM25搜索};

\node[ragmodule, right=1.6cm of hs3, text width=2.5cm, minimum height=1.6cm] (qo1) {查询预处理\\标准化};
\node[ragmodule, right=of qo1, text width=2.5cm, minimum height=1.6cm] (qo2) {相关性计算\\多维度评分};
\node[ragmodule, right=of qo2, text width=2.5cm, minimum height=1.6cm] (qo3) {结果后处理\\去重排序};

% KG详细模块 - 中间部分，横向排列
\node[kgmodule, below=2.8cm of dp1, text width=2.5cm, minimum height=1.6cm] (sd1) {数据模式提取\\表结构分析};
\node[kgmodule, right=of sd1, text width=2.5cm, minimum height=1.6cm] (sd2) {实体配置\\LLM生成};
\node[kgmodule, right=of sd2, text width=2.5cm, minimum height=1.6cm] (sd3) {关系发现\\元图创建};

\node[kgmodule, right=1.6cm of sd3, text width=2.5cm, minimum height=1.6cm] (kb1) {Neo4j连接\\会话管理};
\node[kgmodule, right=of kb1, text width=2.5cm, minimum height=1.6cm] (kb2) {实体创建\\批量导入};
\node[kgmodule, right=of kb2, text width=2.5cm, minimum height=1.6cm] (kb3) {关系构建\\数据链接};

\node[kgmodule, right=1.6cm of kb3, text width=2.5cm, minimum height=1.6cm] (kq1) {意图识别\\实体提取};
\node[kgmodule, right=of kq1, text width=2.5cm, minimum height=1.6cm] (kq2) {相似度匹配\\APOC插件};
\node[kgmodule, right=of kq2, text width=2.5cm, minimum height=1.6cm] (kq3) {Cypher查询\\结果处理};

% Fusion详细模块 - 下半部分，横向排列
\node[fusionmodule, below=2.8cm of sd1, text width=2.5cm, minimum height=1.6cm] (f1) {并行查询执行\\异步调度};
\node[fusionmodule, right=of f1, text width=2.5cm, minimum height=1.6cm] (f2) {结果融合算法\\权重分配};
\node[fusionmodule, right=of f2, text width=2.5cm, minimum height=1.6cm] (f3) {智能路由\\查询类型识别};

\node[fusionmodule, right=1.6cm of f3, text width=2.5cm, minimum height=1.6cm] (f4) {质量控制\\置信度评估};
\node[fusionmodule, right=of f4, text width=2.5cm, minimum height=1.6cm] (f5) {负载均衡\\模块选择优化};
\node[fusionmodule, right=of f5, text width=2.5cm, minimum height=1.6cm] (f6) {输出标准化\\格式转换};

% 组框
\node[groupbox, fit=(dp1)(dp2)(dp3)] (dpgroup) {};
\node[groupbox, fit=(hs1)(hs2)(hs3)] (hsgroup) {};
\node[groupbox, fit=(qo1)(qo2)(qo3)] (qogroup) {};
\node[groupbox, fit=(sd1)(sd2)(sd3)] (sdgroup) {};
\node[groupbox, fit=(kb1)(kb2)(kb3)] (kbgroup) {};
\node[groupbox, fit=(kq1)(kq2)(kq3)] (kqgroup) {};
\node[groupbox, fit=(f1)(f2)(f3)] (fgroup1) {};
\node[groupbox, fit=(f4)(f5)(f6)] (fgroup2) {};

% 标签
\node[above=0.2cm of dpgroup.north, font=\tiny\bfseries, color=ragblue] {Data Preprocessing};
\node[above=0.2cm of hsgroup.north, font=\tiny\bfseries, color=ragblue] {Hybrid Search};
\node[above=0.2cm of qogroup.north, font=\tiny\bfseries, color=ragblue] {Query Optimization};
\node[above=0.2cm of sdgroup.north, font=\tiny\bfseries, color=kggreen] {Schema Discovery};
\node[above=0.2cm of kbgroup.north, font=\tiny\bfseries, color=kggreen] {KG Builder};
\node[above=0.2cm of kqgroup.north, font=\tiny\bfseries, color=kggreen] {KG Query};
\node[above=0.2cm of fgroup1.north, font=\tiny\bfseries, color=fusionpurple] {Fusion Core};
\node[above=0.2cm of fgroup2.north, font=\tiny\bfseries, color=fusionpurple] {Fusion Control};

\end{tikzpicture}
\caption{Detailed functional modules of KG+RAG+Fusion system. This diagram shows the specific functional components of RAG, KG, and Fusion modules, where each module contains multiple specialized sub-functions that work together to accomplish knowledge retrieval, reasoning, and intelligent fusion tasks.}
\label{fig:detailed_modules}
\end{figure}

\end{document}
