#!/usr/bin/env python3
"""
环境检查脚本
检查KG+RAG对比实验所需的环境和依赖
"""

import os
import sys
import requests
import subprocess
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
        return True
    else:
        print(f"❌ Python版本过低: {version.major}.{version.minor}.{version.micro}")
        print("   需要Python 3.8或更高版本")
        return False

def check_ollama_service():
    """检查Ollama服务"""
    print("\n🤖 检查Ollama服务...")
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get("models", [])
            model_names = [m["name"] for m in models]
            print(f"✅ Ollama服务运行正常，可用模型: {len(model_names)}个")
            
            # 检查qwen2.5:7b模型
            if "qwen2.5:7b" in model_names:
                print("✅ qwen2.5:7b模型已安装")
                return True
            else:
                print("❌ qwen2.5:7b模型未安装")
                print("   请运行: ollama pull qwen2.5:7b")
                return False
        else:
            print(f"❌ Ollama服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Ollama服务: {e}")
        print("   请确保Ollama已启动: ollama serve")
        return False

def check_data_files():
    """检查数据文件"""
    print("\n📂 检查数据文件...")
    
    data_files = [
        "data/MedQA/test-2zh.jsonl",
        "data/MedQA/dev-2zh.jsonl",
        "data/MedQA/train-2zh.jsonl"
    ]
    
    all_exist = True
    for file_path in data_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({file_size:,} bytes)")
        else:
            print(f"❌ {file_path} 不存在")
            all_exist = False
    
    return all_exist

def check_required_packages():
    """检查必需的Python包"""
    print("\n📦 检查Python依赖包...")
    
    required_packages = [
        "requests",
        "numpy", 
        "matplotlib",
        "sklearn",
        "pandas"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_kg_system():
    """检查KG系统"""
    print("\n🕸️ 检查KG系统...")
    
    kg_files = [
        "KG/KG_query/main.py",
        "KG/KG_query/kg_query_module.py",
        "KG/KG_query/neo4j_connector.py"
    ]
    
    all_exist = True
    for file_path in kg_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} 不存在")
            all_exist = False
    
    # 检查Neo4j连接（可选）
    try:
        import sys
        sys.path.insert(0, ".")
        from KG.KG_query.neo4j_connector import Neo4jConnector
        connector = Neo4jConnector()
        connector.close()
        print("✅ Neo4j连接测试成功")
    except Exception as e:
        print(f"⚠️ Neo4j连接测试失败: {e}")
        print("   KG功能可能不可用，但不影响其他方法的测试")
    
    return all_exist

def check_rag_system():
    """检查RAG系统"""
    print("\n📚 检查RAG系统...")
    
    rag_files = [
        "RAG/main.py",
        "RAG/milvus_hybrid_search.py"
    ]
    
    all_exist = True
    for file_path in rag_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} 不存在")
            all_exist = False
    
    # 检查Milvus连接（可选）
    try:
        import sys
        sys.path.insert(0, ".")
        from RAG.milvus_hybrid_search import MilvusHybridSearcher
        print("✅ RAG模块导入成功")
    except Exception as e:
        print(f"⚠️ RAG模块导入失败: {e}")
        print("   RAG功能可能不可用，但不影响其他方法的测试")
    
    return all_exist

def main():
    """主检查函数"""
    print("🔍 KG+RAG对比实验环境检查")
    print("=" * 50)
    
    checks = [
        ("Python版本", check_python_version),
        ("Python依赖包", check_required_packages),
        ("Ollama服务", check_ollama_service),
        ("数据文件", check_data_files),
        ("KG系统", check_kg_system),
        ("RAG系统", check_rag_system)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"❌ {check_name} 检查失败: {e}")
            results[check_name] = False
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 检查总结")
    print("=" * 50)
    
    passed = 0
    total = len(checks)
    
    for check_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{check_name:<15} {status}")
        if result:
            passed += 1
    
    print(f"\n通过率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有检查都通过！可以运行对比实验")
        print("运行命令: python main.py")
    elif passed >= 3:  # 至少Python、依赖包、Ollama通过
        print("\n⚠️ 部分检查未通过，但基本功能可用")
        print("可以尝试运行实验，某些功能可能不可用")
        print("运行命令: python main.py")
    else:
        print("\n❌ 关键检查未通过，请先解决环境问题")
        print("建议:")
        print("1. 确保Python 3.8+")
        print("2. 安装依赖: pip install -r requirements_evaluation.txt")
        print("3. 启动Ollama: ollama serve")
        print("4. 安装模型: ollama pull qwen2.5:7b")

if __name__ == "__main__":
    main()
