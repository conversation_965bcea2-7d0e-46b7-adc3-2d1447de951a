# KG+RAG对比实验系统

本系统用于对比评测四种医学问答方法在MedQA数据集上的性能：
1. **无增强** - 直接使用ollama qwen2.5:7b模型
2. **仅KG增强** - 使用知识图谱增强的问答
3. **仅RAG增强** - 使用文档检索增强的问答  
4. **KG+RAG融合** - 结合知识图谱和文档检索的融合问答

## 🚀 快速开始

### 1. 环境检查
```bash
# 检查环境是否就绪
python check_environment.py
```

### 2. 安装依赖
```bash
# 安装Python依赖
pip install -r requirements_evaluation.txt
```

### 3. 启动Ollama服务
```bash
# 启动Ollama服务
ollama serve

# 安装qwen2.5:7b模型（如果未安装）
ollama pull qwen2.5:7b
```

### 4. 运行对比实验
```bash
# 运行完整对比实验
python main.py
```

## 📊 实验配置

### 数据集
- **数据源**: MedQA中文数据集 (`data/MedQA/test-2zh.jsonl`)
- **样本数量**: 默认50个样本（可在main.py中调整`max_samples`参数）
- **问题格式**: 多选题（A、B、C、D四个选项）

### 评测指标
- **准确率 (Accuracy)**: 预测正确的样本比例
- **F1分数 (F1 Score)**: 宏平均和加权平均F1分数
- **精确率 (Precision)**: 宏平均精确率
- **召回率 (Recall)**: 宏平均召回率

### 提示词模板
```
以下是一些医学资料：[f]

给定问题：[q]，以下哪个答案是正确的：[a1, a2, a3, a4]。

您只能用精确的词语输出预测标签。
```

其中：
- `[f]` - 医学资料（KG知识/RAG文档/融合信息）
- `[q]` - 问题文本
- `[a1, a2, a3, a4]` - 四个选项

## 📁 输出结果

实验完成后，结果将保存在 `evaluation_results/` 目录中：

```
evaluation_results/
├── evaluation_results.json      # 详细评测结果
├── comparison_plots.png         # 四指标对比图
└── comprehensive_comparison.png # 综合对比图
```

### 结果文件说明

1. **evaluation_results.json** - 包含每种方法的详细指标
2. **comparison_plots.png** - 四个子图显示准确率、F1分数、精确率、召回率
3. **comprehensive_comparison.png** - 综合对比准确率和F1分数

## ⚙️ 配置参数

在 `main.py` 中可以调整以下参数：

```python
# 主函数中的配置
test_file = "data/MedQA/test-2zh.jsonl"  # 测试数据文件
max_samples = 50                         # 最大测试样本数
output_dir = "evaluation_results"        # 输出目录
```

### 调整样本数量
- **快速测试**: `max_samples = 10`
- **标准测试**: `max_samples = 50` 
- **完整测试**: `max_samples = 100` 或更多

## 🔧 系统要求

### 必需组件
- **Python**: 3.8+
- **Ollama**: 已安装并运行qwen2.5:7b模型
- **数据**: MedQA数据集在正确位置

### 可选组件（用于完整功能）
- **Neo4j**: 用于KG功能（如果不可用，KG方法会回退到无增强）
- **Milvus**: 用于RAG功能（如果不可用，RAG方法会回退到无增强）

## 📈 实验流程

1. **系统初始化** - 检查并初始化KG和RAG系统
2. **数据加载** - 从MedQA数据集加载测试样本
3. **方法评测** - 依次评测四种方法：
   - 无增强方法
   - KG增强方法
   - RAG增强方法
   - KG+RAG融合方法
4. **结果分析** - 计算评测指标并生成对比图表
5. **结果保存** - 保存详细结果和可视化图表

## 🎯 预期结果

实验将输出类似以下的结果总结：

```
🎯 实验总结
================================================================================
排名 方法                   准确率     F1分数     精确率     召回率    
--------------------------------------------------------------------------------
1    KG+RAG Fusion         0.720      0.715      0.718      0.720
2    RAG Only              0.680      0.675      0.678      0.680
3    KG Only               0.640      0.635      0.638      0.640
4    No Enhancement        0.600      0.595      0.598      0.600

🏆 最佳方法: KG+RAG Fusion
   准确率: 0.720
   F1分数: 0.715

📈 相比无增强方法的改进:
   准确率提升: 20.0%
   F1分数提升: 20.2%
```

## 🐛 故障排除

### 常见问题

1. **Ollama连接失败**
   ```bash
   # 确保Ollama服务运行
   ollama serve
   
   # 检查模型是否安装
   ollama list
   ```

2. **KG系统初始化失败**
   - 检查Neo4j是否运行
   - 检查连接配置
   - 系统会自动回退到无增强方法

3. **RAG系统初始化失败**
   - 检查Milvus是否运行
   - 检查向量数据是否已建立
   - 系统会自动回退到无增强方法

4. **内存不足**
   - 减少`max_samples`参数
   - 关闭其他占用内存的程序

### 调试模式

如需查看详细日志，可以在代码中添加调试信息或使用Python调试器。

## 📞 技术支持

如遇到问题，请检查：
1. 环境检查脚本的输出
2. 控制台错误信息
3. 系统资源使用情况
4. 网络连接状态

## 📄 许可证

本项目仅用于学术研究目的。
